import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/album/dto/album_data.dart';
import 'package:portraitmode/album/dto/album_load_more_data.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/appbar/pm_sliver_app_bar.dart';
import 'package:portraitmode/appbar/profile_menu_indicator.dart';
import 'package:portraitmode/artist/dto/artist_data.dart';
import 'package:portraitmode/artist/enum.dart';
import 'package:portraitmode/artist/providers/artist_store_provider.dart';
import 'package:portraitmode/artist/providers/blocked_artists_provider.dart';
import 'package:portraitmode/artist/services/artist_service.dart';
import 'package:portraitmode/artist/widgets/artist_detail/other_profile/album_tabbar.dart';
import 'package:portraitmode/artist/widgets/artist_detail/other_profile/bottom_sheets/other_profile_bottom_sheet.dart';
import 'package:portraitmode/artist/widgets/artist_detail/profile_header.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/base/base_response.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/load_more/enum.dart';
import 'package:portraitmode/load_more/masonry_load_more.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/http_responses/photo_list_response.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';
import 'package:portraitmode/photo/services/photo_list_service.dart';
import 'package:portraitmode/photo/utils/photo_provider_util.dart';
import 'package:portraitmode/photo/widgets/masonry/photo_masonry_item.dart';
import 'package:portraitmode/photo/widgets/photo_detail_screen.dart';

class OtherProfile extends ConsumerStatefulWidget {
  const OtherProfile({
    super.key,
    this.useBackButton = true,
    required this.containerWidth,
    required this.artist,
    this.onAlbumRefresh,
  });

  final bool useBackButton;
  final double containerWidth;
  final ArtistData artist;
  final Function? onAlbumRefresh;

  @override
  OtherProfileState createState() => OtherProfileState();
}

class OtherProfileState extends ConsumerState<OtherProfile> {
  final _photoListService = PhotoListService();

  final _scrollController = ScrollController();

  final List<AlbumLoadMoreData> _loadMoreList = [];

  late List<AlbumData> _albumList;

  static const _loadMorePerPage = LoadMoreConfig.itemsPerPage;

  bool _doingBlockUnblock = false;

  @override
  void initState() {
    _albumList = widget.artist.albums ?? [];

    _albumList.map((AlbumData album) {
      _loadMoreList.add(
        AlbumLoadMoreData(
          slug: album.slug,
          lastId: 0,
          endReached: false,
          photos: [],
        ),
      );
    }).toList();

    super.initState();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: _albumList.length,
      child: Scaffold(
        body: NestedScrollView(
          controller: _scrollController,
          headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
            return [
              SliverOverlapAbsorber(
                handle: NestedScrollView.sliverOverlapAbsorberHandleFor(
                  context,
                ),
              ),
              // This PmSliverAppBar was originally inside of the
              // SliverOverlapAbsorber under it's sliver property.
              // I extracted it out because it was causing weird top gap.
              PmSliverAppBar(
                scrollController: _scrollController,
                automaticallyImplyLeading: widget.useBackButton,
                backgroundColor: context.colors.lightColor,
                titleText: widget.useBackButton
                    ? '@${widget.artist.nicename}'
                    : '',
                useLogo: widget.useBackButton ? false : true,
                actions: [
                  ProfileMenuIndicator(
                    onTap: () {
                      _showOtherProfileBottomSheet();
                    },
                  ),
                ],
              ),
              SliverToBoxAdapter(
                child: Container(
                  color: context.colors.lightColor,
                  padding: const EdgeInsets.only(
                    top: LayoutConfig.contentTopGap,
                    right: ScreenStyleConfig.horizontalPadding,
                    bottom: ScreenStyleConfig.verticalPadding + 20.0,
                    left: ScreenStyleConfig.horizontalPadding,
                  ),
                  child: ProfileHeader(artist: widget.artist),
                ),
              ),
              SliverPersistentHeader(
                pinned: true,
                delegate: AlbumTabbar(albumList: _albumList),
              ),
            ];
          },
          body: TabBarView(
            children: _albumList.map((AlbumData album) {
              int albumIndex = _albumList.indexOf(album);

              return SafeArea(
                top: false,
                bottom: false,
                child: RefreshIndicator(
                  onRefresh: () async {
                    return _handleRefresh(
                      albumIndex: albumIndex,
                      albumSlug: album.slug,
                    );
                  },
                  child: Builder(
                    // This Builder is needed to provide a BuildContext that is
                    // "inside" the NestedScrollView, so that
                    // sliverOverlapAbsorberHandleFor() above
                    // can find the NestedScrollView.
                    builder: (BuildContext context) {
                      return MasonryLoadMore(
                        isFinished: _loadMoreList[albumIndex].endReached,
                        onLoadMore: () async {
                          return _handleAlbumPhotoListLoadMore(
                            slug: _loadMoreList[albumIndex].slug,
                            lastId: _loadMoreList[albumIndex].lastId,
                            endReached: _loadMoreList[albumIndex].endReached,
                          );
                        },
                        loadingWidgetColor: context.colors.baseColorAlt,
                        runOnEmptyResult: true,
                        loadingStatusText: "",
                        finishedStatusText: "",
                        padding: const EdgeInsets.symmetric(
                          vertical: 8.0,
                          horizontal: 8.0,
                        ),
                        crossAxisCount: 2,
                        mainAxisSpacing: 8.0,
                        crossAxisSpacing: 8.0,
                        itemsCount: _loadMoreList[albumIndex].photos.length,
                        itemBuilder: (BuildContext masonryContext, int index) {
                          if (index >=
                              _loadMoreList[albumIndex].photos.length) {
                            return const SizedBox.shrink();
                          }

                          return PhotoMasonryItem(
                            key: ValueKey(
                              _loadMoreList[albumIndex].photos[index].id,
                            ),
                            index: index,
                            photo: _loadMoreList[albumIndex].photos[index],
                            isOwnProfile: false,
                            screenName: 'other_profile',
                            onPhotoTap: () => _handlePhotoTap(
                              _loadMoreList[albumIndex].photos[index],
                            ),
                          );
                        },
                      );
                    },
                  ),
                ),
              );
            }).toList(),
          ),
        ),
      ),
    );
  }

  void _handlePhotoTap(PhotoData photo) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            PhotoDetailScreen(photo: photo, originScreenName: 'other_profile'),
      ),
    );
  }

  Future<void> _handleRefresh({
    required int albumIndex,
    required String albumSlug,
  }) async {
    if (widget.onAlbumRefresh != null) {
      await widget.onAlbumRefresh!();
    }

    final PhotoListResponse response = await _photoListService.fetch(
      albumSlug: albumSlug,
      limit: _loadMorePerPage,
      lastId: 0,
      artistId: widget.artist.id,
    );

    // log('refreshing album $albumSlug of artist ${widget.artist.nicename}');

    _handlePhotoListResponse(albumSlug, response, true);
  }

  Future<LoadMoreResult> _handleAlbumPhotoListLoadMore({
    required String slug,
    required int lastId,
    required bool endReached,
  }) async {
    PhotoListResponse response = await _photoListService.fetch(
      albumSlug: slug,
      limit: _loadMorePerPage,
      lastId: lastId,
      artistId: widget.artist.id,
    );

    _handlePhotoListResponse(slug, response, false);

    if (!response.success) {
      return LoadMoreResult.failed;
    }

    if (response.data.isEmpty || response.data.length < _loadMorePerPage) {
      return LoadMoreResult.finished;
    }

    return LoadMoreResult.success;
  }

  void _handlePhotoListResponse(
    String slug,
    PhotoListResponse response,
    bool isRefresh,
  ) {
    // In case user navigates back immediately before async request complete.
    if (!mounted) return;

    if (!response.success) {
      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      }

      return;
    }

    int albumIndex = _loadMoreList.indexWhere((AlbumLoadMoreData loadMore) {
      return loadMore.slug == slug;
    });

    if (albumIndex == -1) {
      return;
    }

    if (response.data.isEmpty) {
      if (mounted) {
        setState(() {
          _loadMoreList[albumIndex].endReached = true;
        });
      }

      return;
    }

    List<PhotoData> responseData = response.data;

    _loadMoreList[albumIndex].lastId = responseData.last.id;

    ref
        .read(photoStoreProvider.notifier)
        .updateItems(response.data, addIfNotExists: true);

    // log('album $slug of artist ${widget.artist.nicename} loaded ${responseData.length} photos');

    // Sort responseData (photo list) before consuming it (last comes first).
    responseData.sort((a, b) => b.id.compareTo(a.id));

    if (mounted) {
      setState(() {
        _loadMoreList[albumIndex].photos.addAll(responseData);
      });
    }
  }

  void _showOtherProfileBottomSheet() {
    showModalBottomSheet(
      context: context,
      isDismissible: !_doingBlockUnblock,
      // isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return OtherProfileBottomSheet(
          artist: widget.artist.copyWith(
            isBlocked: ref
                .read(artistFieldProvider(widget.artist.id))
                .isBlocked,
          ),
          onBlockArtist: () => _blockUnblockArtist(BlockActionType.block),
          onUnblockArtist: () => _blockUnblockArtist(BlockActionType.unblock),
        );
      },
    );
  }

  void _blockUnblockArtist(BlockActionType action) async {
    if (mounted) {
      setState(() {
        _doingBlockUnblock = true;
      });
    }

    final artistService = ArtistService();

    final BaseResponse response = await artistService.blockUnblockArtist(
      artistId: widget.artist.id,
      action: action,
    );

    if (mounted) {
      setState(() {
        _doingBlockUnblock = false;
      });
    }

    if (!response.success) {
      if (mounted) {
        if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
          showSessionEndedDialog(context, ref);
        } else {
          showErrorDialog(context, ref, message: response.message);
        }
      }

      return;
    }

    ref
        .read(artistStoreProvider.notifier)
        .setIsBlocked(widget.artist.id, action == BlockActionType.block);

    if (action == BlockActionType.unblock) {
      ref.read(blockedArtistIdsProvider.notifier).removeItem(widget.artist.id);
    } else {
      ref.read(blockedArtistIdsProvider.notifier).addItem(widget.artist.id);
      removePhotoListFromAppByAuthorId(ref, widget.artist.id);
    }

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          duration: const Duration(seconds: 2),
          content: Text(response.message),
        ),
      );
    }
  }
}
